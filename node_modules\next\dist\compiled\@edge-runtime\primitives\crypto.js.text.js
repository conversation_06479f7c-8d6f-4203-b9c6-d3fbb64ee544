module.exports = "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/primitives/crypto.js\nvar crypto_exports = {};\n__export(crypto_exports, {\n  Crypto: () => Crypto,\n  CryptoKey: () => CryptoKey,\n  SubtleCrypto: () => SubtleCrypto,\n  crypto: () => crypto\n});\nmodule.exports = __toCommonJS(crypto_exports);\nvar import_node_crypto = require(\"crypto\");\nvar { Crypto, CryptoKey } = import_node_crypto.webcrypto;\nfunction SubtleCrypto() {\n  if (!(this instanceof SubtleCrypto))\n    return new SubtleCrypto();\n  throw TypeError(\"Illegal constructor\");\n}\n__name(SubtleCrypto, \"SubtleCrypto\");\nvar crypto = new Crypto();\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  Crypto,\n  CryptoKey,\n  SubtleCrypto,\n  crypto\n});\n"