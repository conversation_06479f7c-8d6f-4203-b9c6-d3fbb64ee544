(function(){"use strict";var e={876:function(e,t){Object.defineProperty(t,"__esModule",{value:true});0&&0;function _export(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:true,get:t[r]})}_export(t,{hydrationErrorWarning:function(){return r},hydrationErrorComponentStack:function(){return n},patchConsoleError:function(){return patchConsoleError}});let r;let n;const o=new Set(['Warning: Text content did not match. Server: "%s" Client: "%s"%s',"Warning: Expected server HTML to contain a matching <%s> in <%s>.%s",'Warning: Expected server HTML to contain a matching text node for "%s" in <%s>.%s',"Warning: Did not expect server HTML to contain a <%s> in <%s>.%s",'Warning: Did not expect server HTML to contain the text node "%s" in <%s>.%s']);function patchConsoleError(){const e=console.error;console.error=function(t,a,i,l){if(o.has(t)){r=t.replace("%s",a).replace("%s",i).replace("%s","");n=l}e.apply(console,arguments)}}if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},659:function(e,t){Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"parseComponentStack",{enumerable:true,get:function(){return parseComponentStack}});function parseComponentStack(e){const t=[];for(const n of e.trim().split("\n")){const e=/at ([^ ]+)( \((.*)\))?/.exec(n);if(e==null?void 0:e[1]){const n=e[1];const o=e[3];if(o==null?void 0:o.includes("next/dist")){break}const a=o==null?void 0:o.replace(/^(webpack-internal:\/\/\/|file:\/\/)(\(.*\)\/)?/,"");var r;const[i,l,s]=(r=a==null?void 0:a.split(":",3))!=null?r:[];t.push({component:n,file:i,lineNumber:l?Number(l):undefined,column:s?Number(s):undefined})}}return t}if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},204:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.onRefresh=t.onBeforeRefresh=t.unregister=t.register=t.onBuildError=t.onBuildOk=t.ReactDevOverlay=t.getServerError=t.getErrorByType=void 0;const l=a(r(851));const s=r(636);const u=r(659);const c=r(876);(0,c.patchConsoleError)();let d=false;let f=undefined;function onUnhandledError(e){const t=e?.error;if(!t||!(t instanceof Error)||typeof t.stack!=="string"){return}if(t.message.match(/(hydration|content does not match|did not match)/i)){if(c.hydrationErrorWarning){t.message+="\n\n"+c.hydrationErrorWarning}t.message+=`\n\nSee more info here: https://nextjs.org/docs/messages/react-hydration-error`}const r=t;const n=typeof c.hydrationErrorComponentStack==="string"?(0,u.parseComponentStack)(c.hydrationErrorComponentStack).map((e=>e.component)):undefined;l.emit({type:l.TYPE_UNHANDLED_ERROR,reason:t,frames:(0,s.parseStack)(r.stack),componentStack:n})}function onUnhandledRejection(e){const t=e?.reason;if(!t||!(t instanceof Error)||typeof t.stack!=="string"){return}const r=t;l.emit({type:l.TYPE_UNHANDLED_REJECTION,reason:t,frames:(0,s.parseStack)(r.stack)})}function register(){if(d){return}d=true;try{const e=Error.stackTraceLimit;Error.stackTraceLimit=50;f=e}catch{}window.addEventListener("error",onUnhandledError);window.addEventListener("unhandledrejection",onUnhandledRejection)}t.register=register;function unregister(){if(!d){return}d=false;if(f!==undefined){try{Error.stackTraceLimit=f}catch{}f=undefined}window.removeEventListener("error",onUnhandledError);window.removeEventListener("unhandledrejection",onUnhandledRejection)}t.unregister=unregister;function onBuildOk(){l.emit({type:l.TYPE_BUILD_OK})}t.onBuildOk=onBuildOk;function onBuildError(e){l.emit({type:l.TYPE_BUILD_ERROR,message:e})}t.onBuildError=onBuildError;function onRefresh(){l.emit({type:l.TYPE_REFRESH})}t.onRefresh=onRefresh;function onBeforeRefresh(){l.emit({type:l.TYPE_BEFORE_REFRESH})}t.onBeforeRefresh=onBeforeRefresh;var m=r(403);Object.defineProperty(t,"getErrorByType",{enumerable:true,get:function(){return m.getErrorByType}});var p=r(233);Object.defineProperty(t,"getServerError",{enumerable:true,get:function(){return p.getServerError}});var b=r(222);Object.defineProperty(t,"ReactDevOverlay",{enumerable:true,get:function(){return i(b).default}})},790:function(e,t,r){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.ErrorBoundary=void 0;const o=n(r(522));class ErrorBoundary extends o.default.PureComponent{constructor(){super(...arguments);this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){this.props.onError(e,t?.componentStack||null);if(!this.props.globalOverlay){this.setState({error:e})}}render(){return this.state.error||this.props.globalOverlay&&this.props.isMounted?this.props.globalOverlay?o.default.createElement("html",null,o.default.createElement("head",null),o.default.createElement("body",null)):null:this.props.children}}t.ErrorBoundary=ErrorBoundary},222:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});const i=a(r(522));const l=a(r(851));const s=r(338);const u=r(936);const c=r(355);const d=r(790);const f=r(884);const m=r(464);const p=r(495);function pushErrorFilterDuplicates(e,t){return[...e.filter((e=>e.event.reason!==t.event.reason)),t]}function reducer(e,t){switch(t.type){case l.TYPE_BUILD_OK:{return{...e,buildError:null}}case l.TYPE_BUILD_ERROR:{return{...e,buildError:t.message}}case l.TYPE_BEFORE_REFRESH:{return{...e,refreshState:{type:"pending",errors:[]}}}case l.TYPE_REFRESH:{return{...e,buildError:null,errors:e.refreshState.type==="pending"?e.refreshState.errors:[],refreshState:{type:"idle"}}}case l.TYPE_UNHANDLED_ERROR:case l.TYPE_UNHANDLED_REJECTION:{switch(e.refreshState.type){case"idle":{return{...e,nextId:e.nextId+1,errors:pushErrorFilterDuplicates(e.errors,{id:e.nextId,event:t})}}case"pending":{return{...e,nextId:e.nextId+1,refreshState:{...e.refreshState,errors:pushErrorFilterDuplicates(e.refreshState.errors,{id:e.nextId,event:t})}}}default:const r=e.refreshState;return e}}default:{const r=t;return e}}}const shouldPreventDisplay=(e,t)=>{if(!t||!e){return false}return t.includes(e)};const b=function ReactDevOverlay({children:e,preventDisplay:t,globalOverlay:r}){const[n,o]=i.useReducer(reducer,{nextId:1,buildError:null,errors:[],refreshState:{type:"idle"}});i.useEffect((()=>{l.on(o);return function(){l.off(o)}}),[o]);const a=i.useCallback(((e,t)=>{}),[]);const b=n.buildError!=null;const v=Boolean(n.errors.length);const g=b?"build":v?"runtime":null;const h=g!==null;return i.createElement(i.Fragment,null,i.createElement(d.ErrorBoundary,{globalOverlay:r,isMounted:h,onError:a},e??null),h?i.createElement(s.ShadowPortal,{globalOverlay:r},i.createElement(p.CssReset,null),i.createElement(f.Base,null),i.createElement(m.ComponentStyles,null),shouldPreventDisplay(g,t)?null:b?i.createElement(u.BuildError,{message:n.buildError}):v?i.createElement(c.Errors,{errors:n.errors}):undefined):undefined)};t["default"]=b},851:function(e,t){Object.defineProperty(t,"__esModule",{value:true});t.off=t.on=t.emit=t.TYPE_UNHANDLED_REJECTION=t.TYPE_UNHANDLED_ERROR=t.TYPE_BEFORE_REFRESH=t.TYPE_REFRESH=t.TYPE_BUILD_ERROR=t.TYPE_BUILD_OK=void 0;t.TYPE_BUILD_OK="build-ok";t.TYPE_BUILD_ERROR="build-error";t.TYPE_REFRESH="fast-refresh";t.TYPE_BEFORE_REFRESH="before-fast-refresh";t.TYPE_UNHANDLED_ERROR="unhandled-error";t.TYPE_UNHANDLED_REJECTION="unhandled-rejection";let r=new Set;let n=[];function drain(){setTimeout((function(){while(Boolean(n.length)&&Boolean(r.size)){const e=n.shift();r.forEach((t=>t(e)))}}),1)}function emit(e){n.push(Object.freeze({...e}));drain()}t.emit=emit;function on(e){if(r.has(e)){return false}r.add(e);drain();return true}t.on=on;function off(e){if(r.has(e)){r.delete(e);return true}return false}t.off=off},987:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.CodeFrame=void 0;const l=i(r(997));const s=a(r(522));const u=i(r(518));const c=r(504);const d=function CodeFrame({stackFrame:e,codeFrame:t}){const r=s.useMemo((()=>{const e=t.split(/\r?\n/g);const r=e.map((e=>/^>? +\d+ +\| [ ]+/.exec((0,u.default)(e))===null?null:/^>? +\d+ +\| ( *)/.exec((0,u.default)(e)))).filter(Boolean).map((e=>e.pop())).reduce(((e,t)=>isNaN(e)?t.length:Math.min(e,t.length)),NaN);if(r>1){const t=" ".repeat(r);return e.map(((e,r)=>~(r=e.indexOf("|"))?e.substring(0,r)+e.substring(r).replace(t,""):e)).join("\n")}return e.join("\n")}),[t]);const n=s.useMemo((()=>l.default.ansiToJson(r,{json:true,use_classes:true,remove_empty:true})),[r]);const o=s.useCallback((()=>{const t=new URLSearchParams;for(const r in e){t.append(r,(e[r]??"").toString())}self.fetch(`${process.env.__NEXT_ROUTER_BASEPATH||""}/__nextjs_launch-editor?${t.toString()}`).then((()=>{}),(()=>{console.error("There was an issue opening this code in your editor.")}))}),[e]);return s.createElement("div",{"data-nextjs-codeframe":true},s.createElement("div",null,s.createElement("p",{role:"link",onClick:o,tabIndex:1,title:"Click to open in your editor"},s.createElement("span",null,(0,c.getFrameSource)(e)," @ ",e.methodName),s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s.createElement("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),s.createElement("polyline",{points:"15 3 21 3 21 9"}),s.createElement("line",{x1:"10",y1:"14",x2:"21",y2:"3"})))),s.createElement("pre",null,n.map(((e,t)=>s.createElement("span",{key:`frame-${t}`,style:{color:e.fg?`var(--color-${e.fg})`:undefined,...e.decoration==="bold"?{fontWeight:800}:e.decoration==="italic"?{fontStyle:"italic"}:undefined}},e.content)))))};t.CodeFrame=d},413:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.CodeFrame=void 0;var n=r(987);Object.defineProperty(t,"CodeFrame",{enumerable:true,get:function(){return n.CodeFrame}})},399:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=void 0;const n=r(910);const o=(0,n.noop)`
  [data-nextjs-codeframe] {
    overflow: auto;
    border-radius: var(--size-gap-half);
    background-color: var(--color-ansi-bg);
    color: var(--color-ansi-fg);
  }
  [data-nextjs-codeframe]::selection,
  [data-nextjs-codeframe] *::selection {
    background-color: var(--color-ansi-selection);
  }
  [data-nextjs-codeframe] * {
    color: inherit;
    background-color: transparent;
    font-family: var(--font-stack-monospace);
  }

  [data-nextjs-codeframe] > * {
    margin: 0;
    padding: calc(var(--size-gap) + var(--size-gap-half))
      calc(var(--size-gap-double) + var(--size-gap-half));
  }
  [data-nextjs-codeframe] > div {
    display: inline-block;
    width: auto;
    min-width: 100%;
    border-bottom: 1px solid var(--color-ansi-bright-black);
  }
  [data-nextjs-codeframe] > div > p {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    margin: 0;
  }
  [data-nextjs-codeframe] > div > p:hover {
    text-decoration: underline dotted;
  }
  [data-nextjs-codeframe] div > p > svg {
    width: auto;
    height: 1em;
    margin-left: 8px;
  }
  [data-nextjs-codeframe] div > pre {
    overflow: hidden;
    display: inline-block;
  }
`;t.styles=o},616:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.Dialog=void 0;const i=a(r(522));const l=r(169);const s=function Dialog({children:e,type:t,onClose:r,...n}){const[o,a]=i.useState(null);const[s,u]=i.useState(typeof document!=="undefined"&&document.hasFocus()?"dialog":undefined);const c=i.useCallback((e=>{a(e)}),[]);(0,l.useOnClickOutside)(o,r);i.useEffect((()=>{if(o==null){return}const e=o.getRootNode();if(!(e instanceof ShadowRoot)){return}const t=e;function handler(e){const r=t.activeElement;if(e.key==="Enter"&&r instanceof HTMLElement&&r.getAttribute("role")==="link"){e.preventDefault();e.stopPropagation();r.click()}}function handleFocus(){u(document.hasFocus()?"dialog":undefined)}t.addEventListener("keydown",handler);window.addEventListener("focus",handleFocus);window.addEventListener("blur",handleFocus);return()=>{t.removeEventListener("keydown",handler);window.removeEventListener("focus",handleFocus);window.removeEventListener("blur",handleFocus)}}),[o]);return i.createElement("div",{ref:c,"data-nextjs-dialog":true,tabIndex:-1,role:s,"aria-labelledby":n["aria-labelledby"],"aria-describedby":n["aria-describedby"],"aria-modal":"true"},i.createElement("div",{"data-nextjs-dialog-banner":true,className:`banner-${t}`}),e)};t.Dialog=s},11:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.DialogBody=void 0;const i=a(r(522));const l=function DialogBody({children:e,className:t}){return i.createElement("div",{"data-nextjs-dialog-body":true,className:t},e)};t.DialogBody=l},991:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.DialogContent=void 0;const i=a(r(522));const l=function DialogContent({children:e,className:t}){return i.createElement("div",{"data-nextjs-dialog-content":true,className:t},e)};t.DialogContent=l},342:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.DialogHeader=void 0;const i=a(r(522));const l=function DialogHeader({children:e,className:t}){return i.createElement("div",{"data-nextjs-dialog-header":true,className:t},e)};t.DialogHeader=l},651:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=t.DialogHeader=t.DialogContent=t.DialogBody=t.Dialog=void 0;var n=r(616);Object.defineProperty(t,"Dialog",{enumerable:true,get:function(){return n.Dialog}});var o=r(11);Object.defineProperty(t,"DialogBody",{enumerable:true,get:function(){return o.DialogBody}});var a=r(991);Object.defineProperty(t,"DialogContent",{enumerable:true,get:function(){return a.DialogContent}});var i=r(342);Object.defineProperty(t,"DialogHeader",{enumerable:true,get:function(){return i.DialogHeader}});var l=r(213);Object.defineProperty(t,"styles",{enumerable:true,get:function(){return l.styles}})},213:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=void 0;const n=r(910);const o=(0,n.noop)`
  [data-nextjs-dialog] {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    outline: none;
    background: var(--color-background);
    border-radius: var(--size-gap);
    box-shadow: 0 var(--size-gap-half) var(--size-gap-double)
      rgba(0, 0, 0, 0.25);
    max-height: calc(100% - 56px);
    overflow-y: hidden;
  }

  @media (max-height: 812px) {
    [data-nextjs-dialog-overlay] {
      max-height: calc(100% - 15px);
    }
  }

  @media (min-width: 576px) {
    [data-nextjs-dialog] {
      max-width: 540px;
      box-shadow: 0 var(--size-gap) var(--size-gap-quad) rgba(0, 0, 0, 0.25);
    }
  }

  @media (min-width: 768px) {
    [data-nextjs-dialog] {
      max-width: 720px;
    }
  }

  @media (min-width: 992px) {
    [data-nextjs-dialog] {
      max-width: 960px;
    }
  }

  [data-nextjs-dialog-banner] {
    position: relative;
  }
  [data-nextjs-dialog-banner].banner-warning {
    border-color: var(--color-ansi-yellow);
  }
  [data-nextjs-dialog-banner].banner-error {
    border-color: var(--color-ansi-red);
  }

  [data-nextjs-dialog-banner]::after {
    z-index: 2;
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    /* banner width: */
    border-top-width: var(--size-gap-half);
    border-bottom-width: 0;
    border-top-style: solid;
    border-bottom-style: solid;
    border-top-color: inherit;
    border-bottom-color: transparent;
  }

  [data-nextjs-dialog-content] {
    overflow-y: auto;
    border: none;
    margin: 0;
    /* calc(padding + banner width offset) */
    padding: calc(var(--size-gap-double) + var(--size-gap-half))
      var(--size-gap-double);
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {
    flex-shrink: 0;
    margin-bottom: var(--size-gap-double);
  }
  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {
    position: relative;
    flex: 1 1 auto;
  }
`;t.styles=o},831:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.LeftRightDialogHeader=void 0;const i=a(r(522));const l=r(865);const s=function LeftRightDialogHeader({children:e,className:t,previous:r,next:n,close:o}){const a=i.useRef(null);const s=i.useRef(null);const u=i.useRef(null);const[c,d]=i.useState(null);const f=i.useCallback((e=>{d(e)}),[]);i.useEffect((()=>{if(c==null){return}const e=c.getRootNode();const t=self.document;function handler(t){if(t.key==="ArrowLeft"){t.stopPropagation();if(a.current){a.current.focus()}r&&r()}else if(t.key==="ArrowRight"){t.stopPropagation();if(s.current){s.current.focus()}n&&n()}else if(t.key==="Escape"){t.stopPropagation();if(e instanceof ShadowRoot){const t=e.activeElement;if(t&&t!==u.current&&t instanceof HTMLElement){t.blur();return}}if(o){o()}}}e.addEventListener("keydown",handler);if(e!==t){t.addEventListener("keydown",handler)}return function(){e.removeEventListener("keydown",handler);if(e!==t){t.removeEventListener("keydown",handler)}}}),[o,c,n,r]);i.useEffect((()=>{if(c==null){return}const e=c.getRootNode();if(e instanceof ShadowRoot){const t=e.activeElement;if(r==null){if(a.current&&t===a.current){a.current.blur()}}else if(n==null){if(s.current&&t===s.current){s.current.blur()}}}}),[c,n,r]);return i.createElement("div",{"data-nextjs-dialog-left-right":true,className:t},i.createElement("nav",{ref:f},i.createElement("button",{ref:a,type:"button",disabled:r==null?true:undefined,"aria-disabled":r==null?true:undefined,onClick:r??undefined},i.createElement("svg",{viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i.createElement("title",null,"previous"),i.createElement("path",{d:"M6.99996 1.16666L1.16663 6.99999L6.99996 12.8333M12.8333 6.99999H1.99996H12.8333Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))),i.createElement("button",{ref:s,type:"button",disabled:n==null?true:undefined,"aria-disabled":n==null?true:undefined,onClick:n??undefined},i.createElement("svg",{viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i.createElement("title",null,"next"),i.createElement("path",{d:"M6.99996 1.16666L12.8333 6.99999L6.99996 12.8333M1.16663 6.99999H12H1.16663Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})))," ",e),o?i.createElement("button",{"data-nextjs-errors-dialog-left-right-close-button":true,ref:u,type:"button",onClick:o,"aria-label":"Close"},i.createElement("span",{"aria-hidden":"true"},i.createElement(l.CloseIcon,null))):null)};t.LeftRightDialogHeader=s},732:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=t.LeftRightDialogHeader=void 0;var n=r(831);Object.defineProperty(t,"LeftRightDialogHeader",{enumerable:true,get:function(){return n.LeftRightDialogHeader}});var o=r(543);Object.defineProperty(t,"styles",{enumerable:true,get:function(){return o.styles}})},543:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=void 0;const n=r(910);const o=(0,n.noop)`
  [data-nextjs-dialog-left-right] {
    display: flex;
    flex-direction: row;
    align-content: center;
    align-items: center;
    justify-content: space-between;
  }
  [data-nextjs-dialog-left-right] > nav > button {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    width: calc(var(--size-gap-double) + var(--size-gap));
    height: calc(var(--size-gap-double) + var(--size-gap));
    font-size: 0;
    border: none;
    background-color: rgba(255, 85, 85, 0.1);
    color: var(--color-ansi-red);
    cursor: pointer;
    transition: background-color 0.25s ease;
  }
  [data-nextjs-dialog-left-right] > nav > button > svg {
    width: auto;
    height: calc(var(--size-gap) + var(--size-gap-half));
  }
  [data-nextjs-dialog-left-right] > nav > button:hover {
    background-color: rgba(255, 85, 85, 0.2);
  }
  [data-nextjs-dialog-left-right] > nav > button:disabled {
    background-color: rgba(255, 85, 85, 0.1);
    color: rgba(255, 85, 85, 0.4);
    cursor: not-allowed;
  }

  [data-nextjs-dialog-left-right] > nav > button:first-of-type {
    border-radius: var(--size-gap-half) 0 0 var(--size-gap-half);
    margin-right: 1px;
  }
  [data-nextjs-dialog-left-right] > nav > button:last-of-type {
    border-radius: 0 var(--size-gap-half) var(--size-gap-half) 0;
  }

  [data-nextjs-dialog-left-right] > button:last-of-type {
    border: 0;
    padding: 0;

    background-color: transparent;
    appearance: none;

    opacity: 0.4;
    transition: opacity 0.25s ease;

    color: var(--color-font);
  }
  [data-nextjs-dialog-left-right] > button:last-of-type:hover {
    opacity: 0.7;
  }
`;t.styles=o},17:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.Overlay=void 0;const l=i(r(975));const s=a(r(522));const u=r(800);const c=function Overlay({className:e,children:t,fixed:r}){s.useEffect((()=>{(0,u.lock)();return()=>{(0,u.unlock)()}}),[]);const[n,o]=s.useState(null);const a=s.useCallback((e=>{o(e)}),[]);s.useEffect((()=>{if(n==null){return}const e=(0,l.default)({context:n});return()=>{e.disengage()}}),[n]);return s.createElement("div",{"data-nextjs-dialog-overlay":true,className:e,ref:a},s.createElement("div",{"data-nextjs-dialog-backdrop":true,"data-nextjs-dialog-backdrop-fixed":r?true:undefined}),t)};t.Overlay=c},800:function(e,t){Object.defineProperty(t,"__esModule",{value:true});t.unlock=t.lock=void 0;let r;let n;let o=0;function lock(){setTimeout((()=>{if(o++>0){return}const e=window.innerWidth-document.documentElement.clientWidth;if(e>0){r=document.body.style.paddingRight;document.body.style.paddingRight=`${e}px`}n=document.body.style.overflow;document.body.style.overflow="hidden"}))}t.lock=lock;function unlock(){setTimeout((()=>{if(o===0||--o!==0){return}if(r!==undefined){document.body.style.paddingRight=r;r=undefined}if(n!==undefined){document.body.style.overflow=n;n=undefined}}))}t.unlock=unlock},278:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.Overlay=void 0;var n=r(17);Object.defineProperty(t,"Overlay",{enumerable:true,get:function(){return n.Overlay}})},975:function(e,t,r){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});const o=n(r(709));const a=n(r(292));function nodeArray(e){if(!e){return[]}if(Array.isArray(e)){return e}if(e.nodeType!==undefined){return[e]}if(typeof e==="string"){e=document.querySelectorAll(e)}if(e.length!==undefined){return[].slice.call(e,0)}throw new TypeError("unexpected input "+String(e))}function contextToElement(e){var t=e.context,r=e.label,n=r===undefined?"context-to-element":r,o=e.resolveDocument,a=e.defaultToDocument;var i=nodeArray(t)[0];if(o&&i&&i.nodeType===Node.DOCUMENT_NODE){i=i.documentElement}if(!i&&a){return document.documentElement}if(!i){throw new TypeError(n+" requires valid options.context")}if(i.nodeType!==Node.ELEMENT_NODE&&i.nodeType!==Node.DOCUMENT_FRAGMENT_NODE){throw new TypeError(n+" requires options.context to be an Element")}return i}function getShadowHost(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context;var r=contextToElement({label:"get/shadow-host",context:t});var n=null;while(r){n=r;r=r.parentNode}if(n.nodeType===n.DOCUMENT_FRAGMENT_NODE&&n.host){return n.host}return null}function getDocument(e){if(!e){return document}if(e.nodeType===Node.DOCUMENT_NODE){return e}return e.ownerDocument||document}function isActiveElement(e){var t=contextToElement({label:"is/active-element",resolveDocument:true,context:e});var r=getDocument(t);if(r.activeElement===t){return true}var n=getShadowHost({context:t});if(n&&n.shadowRoot.activeElement===t){return true}return false}function getParents(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context;var r=[];var n=contextToElement({label:"get/parents",context:t});while(n){r.push(n);n=n.parentNode;if(n&&n.nodeType!==Node.ELEMENT_NODE){n=null}}return r}var i=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector"];var l=null;function findMethodName(e){i.some((function(t){if(!e[t]){return false}l=t;return true}))}function elementMatches(e,t){if(!l){findMethodName(e)}return e[l](t)}var s=JSON.parse(JSON.stringify(o.default));var u=s.os.family||"";var c=u==="Android";var d=u.slice(0,7)==="Windows";var f=u==="OS X";var m=u==="iOS";var p=s.layout==="Blink";var b=s.layout==="Gecko";var v=s.layout==="Trident";var g=s.layout==="EdgeHTML";var h=s.layout==="WebKit";var y=parseFloat(s.version);var x=Math.floor(y);s.majorVersion=x;s.is={ANDROID:c,WINDOWS:d,OSX:f,IOS:m,BLINK:p,GECKO:b,TRIDENT:v,EDGE:g,WEBKIT:h,IE9:v&&x===9,IE10:v&&x===10,IE11:v&&x===11};function before(){var e={activeElement:document.activeElement,windowScrollTop:window.scrollTop,windowScrollLeft:window.scrollLeft,bodyScrollTop:document.body.scrollTop,bodyScrollLeft:document.body.scrollLeft};var t=document.createElement("iframe");t.setAttribute("style","position:absolute; position:fixed; top:0; left:-2px; width:1px; height:1px; overflow:hidden;");t.setAttribute("aria-live","off");t.setAttribute("aria-busy","true");t.setAttribute("aria-hidden","true");document.body.appendChild(t);var r=t.contentWindow;var n=r.document;n.open();n.close();var o=n.createElement("div");n.body.appendChild(o);e.iframe=t;e.wrapper=o;e.window=r;e.document=n;return e}function test(e,t){e.wrapper.innerHTML="";var r=typeof t.element==="string"?e.document.createElement(t.element):t.element(e.wrapper,e.document);var n=t.mutate&&t.mutate(r,e.wrapper,e.document);if(!n&&n!==false){n=r}!r.parentNode&&e.wrapper.appendChild(r);n&&n.focus&&n.focus();return t.validate?t.validate(r,n,e.document):e.document.activeElement===n}function after(e){if(e.activeElement===document.body){document.activeElement&&document.activeElement.blur&&document.activeElement.blur();if(s.is.IE10){document.body.focus()}}else{e.activeElement&&e.activeElement.focus&&e.activeElement.focus()}document.body.removeChild(e.iframe);window.scrollTop=e.windowScrollTop;window.scrollLeft=e.windowScrollLeft;document.body.scrollTop=e.bodyScrollTop;document.body.scrollLeft=e.bodyScrollLeft}function detectFocus(e){var t=before();var r={};Object.keys(e).map((function(n){r[n]=test(t,e[n])}));after(t);return r}var E="1.4.1";function readLocalStorage(e){var t=void 0;try{t=window.localStorage&&window.localStorage.getItem(e);t=t?JSON.parse(t):{}}catch(e){t={}}return t}function writeLocalStorage(e,t){if(!document.hasFocus()){try{window.localStorage&&window.localStorage.removeItem(e)}catch(e){}return}try{window.localStorage&&window.localStorage.setItem(e,JSON.stringify(t))}catch(e){}}var _=typeof window!=="undefined"&&window.navigator.userAgent||"";var w="ally-supports-cache";var O=readLocalStorage(w);if(O.userAgent!==_||O.version!==E){O={}}O.userAgent=_;O.version=E;var j={get:function get(){return O},set:function set(e){Object.keys(e).forEach((function(t){O[t]=e[t]}));O.time=(new Date).toISOString();writeLocalStorage(w,O)}};function cssShadowPiercingDeepCombinator(){var e=void 0;try{document.querySelector("html >>> :first-child");e=">>>"}catch(t){try{document.querySelector("html /deep/ :first-child");e="/deep/"}catch(t){e=""}}return e}var k="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";var S={element:"div",mutate:function mutate(e){e.innerHTML='<map name="image-map-tabindex-test">'+'<area shape="rect" coords="63,19,144,45"></map>'+'<img usemap="#image-map-tabindex-test" tabindex="-1" alt="" src="'+k+'">';return e.querySelector("area")}};var T={element:"div",mutate:function mutate(e){e.innerHTML='<map name="image-map-tabindex-test">'+'<area href="#void" tabindex="-1" shape="rect" coords="63,19,144,45"></map>'+'<img usemap="#image-map-tabindex-test" alt="" src="'+k+'">';return false},validate:function validate(e,t,r){if(s.is.GECKO){return true}var n=e.querySelector("area");n.focus();return r.activeElement===n}};var C={element:"div",mutate:function mutate(e){e.innerHTML='<map name="image-map-area-href-test">'+'<area shape="rect" coords="63,19,144,45"></map>'+'<img usemap="#image-map-area-href-test" alt="" src="'+k+'">';return e.querySelector("area")},validate:function validate(e,t,r){if(s.is.GECKO){return true}return r.activeElement===t}};var D={name:"can-focus-audio-without-controls",element:"audio",mutate:function mutate(e){try{e.setAttribute("src",k)}catch(e){}}};var P="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ";var M={element:"div",mutate:function mutate(e){e.innerHTML='<map name="broken-image-map-test"><area href="#void" shape="rect" coords="63,19,144,45"></map>'+'<img usemap="#broken-image-map-test" alt="" src="'+P+'">';return e.querySelector("area")}};var N={element:"div",mutate:function mutate(e){e.setAttribute("tabindex","-1");e.setAttribute("style","display: -webkit-flex; display: -ms-flexbox; display: flex;");e.innerHTML='<span style="display: block;">hello</span>';return e.querySelector("span")}};var A={element:"fieldset",mutate:function mutate(e){e.setAttribute("tabindex",0);e.setAttribute("disabled","disabled")}};var R={element:"fieldset",mutate:function mutate(e){e.innerHTML="<legend>legend</legend><p>content</p>"}};var F={element:"span",mutate:function mutate(e){e.setAttribute("style","display: -webkit-flex; display: -ms-flexbox; display: flex;");e.innerHTML='<span style="display: block;">hello</span>'}};var I={element:"form",mutate:function mutate(e){e.setAttribute("tabindex",0);e.setAttribute("disabled","disabled")}};var L={element:"a",mutate:function mutate(e){e.href="#void";e.innerHTML='<img ismap src="'+k+'" alt="">';return e.querySelector("img")}};var B={element:"div",mutate:function mutate(e){e.innerHTML='<map name="image-map-tabindex-test"><area href="#void" shape="rect" coords="63,19,144,45"></map>'+'<img usemap="#image-map-tabindex-test" tabindex="-1" alt="" '+'src="'+k+'">';return e.querySelector("img")}};var H={element:function element(e,t){var r=t.createElement("iframe");e.appendChild(r);var n=r.contentWindow.document;n.open();n.close();return r},mutate:function mutate(e){e.style.visibility="hidden";var t=e.contentWindow.document;var r=t.createElement("input");t.body.appendChild(r);return r},validate:function validate(e){var t=e.contentWindow.document;var r=t.querySelector("input");return t.activeElement===r}};var z=!s.is.WEBKIT;function focusInZeroDimensionObject(){return z}var q={element:"div",mutate:function mutate(e){e.setAttribute("tabindex","invalid-value")}};var W={element:"label",mutate:function mutate(e){e.setAttribute("tabindex","-1")},validate:function validate(e,t,r){var n=e.offsetHeight;e.focus();return r.activeElement===e}};var U="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtb"+"G5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBpZD0ic3ZnIj48dGV4dCB4PSIxMCIgeT0iMjAiIGlkPSJ"+"zdmctbGluay10ZXh0Ij50ZXh0PC90ZXh0Pjwvc3ZnPg==";var V={element:"object",mutate:function mutate(e){e.setAttribute("type","image/svg+xml");e.setAttribute("data",U);e.setAttribute("width","200");e.setAttribute("height","50");e.style.visibility="hidden"}};var $={name:"can-focus-object-svg",element:"object",mutate:function mutate(e){e.setAttribute("type","image/svg+xml");e.setAttribute("data",U);e.setAttribute("width","200");e.setAttribute("height","50")},validate:function validate(e,t,r){if(s.is.GECKO){return true}return r.activeElement===e}};var K=!s.is.IE9;function focusObjectSwf(){return K}var Z={element:"div",mutate:function mutate(e){e.innerHTML='<map name="focus-redirect-img-usemap"><area href="#void" shape="rect" coords="63,19,144,45"></map>'+'<img usemap="#focus-redirect-img-usemap" alt="" '+'src="'+k+'">';return e.querySelector("img")},validate:function validate(e,t,r){var n=e.querySelector("area");return r.activeElement===n}};var G={element:"fieldset",mutate:function mutate(e){e.innerHTML='<legend>legend</legend><input tabindex="-1"><input tabindex="0">';return false},validate:function validate(e,t,r){var n=e.querySelector('input[tabindex="-1"]');var o=e.querySelector('input[tabindex="0"]');e.focus();e.querySelector("legend").focus();return r.activeElement===n&&"focusable"||r.activeElement===o&&"tabbable"||""}};var Y={element:"div",mutate:function mutate(e){e.setAttribute("style","width: 100px; height: 50px; overflow: auto;");e.innerHTML='<div style="width: 500px; height: 40px;">scrollable content</div>';return e.querySelector("div")}};var J={element:"div",mutate:function mutate(e){e.setAttribute("style","width: 100px; height: 50px;");e.innerHTML='<div style="width: 500px; height: 40px;">scrollable content</div>'}};var X={element:"div",mutate:function mutate(e){e.setAttribute("style","width: 100px; height: 50px; overflow: auto;");e.innerHTML='<div style="width: 500px; height: 40px;">scrollable content</div>'}};var Q={element:"details",mutate:function mutate(e){e.innerHTML="<summary>foo</summary><p>content</p>";return e.firstElementChild}};function makeFocusableForeignObject(){var e=document.createElementNS("http://www.w3.org/2000/svg","foreignObject");e.width.baseVal.value=30;e.height.baseVal.value=30;e.appendChild(document.createElement("input"));e.lastChild.type="text";return e}function focusSvgForeignObjectHack(e){var t=e.ownerSVGElement||e.nodeName.toLowerCase()==="svg";if(!t){return false}var r=makeFocusableForeignObject();e.appendChild(r);var n=r.querySelector("input");n.focus();n.disabled=true;e.removeChild(r);return true}function generate(e){return'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">'+e+"</svg>"}function focus(e){if(e.focus){return}try{HTMLElement.prototype.focus.call(e)}catch(t){focusSvgForeignObjectHack(e)}}function validate(e,t,r){focus(t);return r.activeElement===t}var ee={element:"div",mutate:function mutate(e){e.innerHTML=generate('<text focusable="true">a</text>');return e.querySelector("text")},validate:validate};var te={element:"div",mutate:function mutate(e){e.innerHTML=generate('<text tabindex="0">a</text>');return e.querySelector("text")},validate:validate};var re={element:"div",mutate:function mutate(e){e.innerHTML=generate('<text tabindex="-1">a</text>');return e.querySelector("text")},validate:validate};var ne={element:"div",mutate:function mutate(e){e.innerHTML=generate(['<g id="ally-test-target"><a xlink:href="#void"><text>link</text></a></g>','<use xlink:href="#ally-test-target" x="0" y="0" tabindex="-1" />'].join(""));return e.querySelector("use")},validate:validate};var oe={element:"div",mutate:function mutate(e){e.innerHTML=generate('<foreignObject tabindex="-1"><input type="text" /></foreignObject>');return e.querySelector("foreignObject")||e.getElementsByTagName("foreignObject")[0]},validate:validate};var ae=Boolean(s.is.GECKO&&typeof SVGElement!=="undefined"&&SVGElement.prototype.focus);function focusSvgInIframe(){return ae}var ie={element:"div",mutate:function mutate(e){e.innerHTML=generate("");return e.firstChild},validate:validate};var le={element:"div",mutate:function mutate(e){e.setAttribute("tabindex","3x")}};var se={element:"table",mutate:function mutate(e,t,r){var n=r.createDocumentFragment();n.innerHTML="<tr><td>cell</td></tr>";e.appendChild(n)}};var ue={element:"video",mutate:function mutate(e){try{e.setAttribute("src",k)}catch(e){}}};var ce=s.is.GECKO||s.is.TRIDENT||s.is.EDGE;function tabsequenceAreaAtImgPosition(){return ce}var de={cssShadowPiercingDeepCombinator:cssShadowPiercingDeepCombinator,focusInZeroDimensionObject:focusInZeroDimensionObject,focusObjectSwf:focusObjectSwf,focusSvgInIframe:focusSvgInIframe,tabsequenceAreaAtImgPosition:tabsequenceAreaAtImgPosition};var fe={focusAreaImgTabindex:S,focusAreaTabindex:T,focusAreaWithoutHref:C,focusAudioWithoutControls:D,focusBrokenImageMap:M,focusChildrenOfFocusableFlexbox:N,focusFieldsetDisabled:A,focusFieldset:R,focusFlexboxContainer:F,focusFormDisabled:I,focusImgIsmap:L,focusImgUsemapTabindex:B,focusInHiddenIframe:H,focusInvalidTabindex:q,focusLabelTabindex:W,focusObjectSvg:$,focusObjectSvgHidden:V,focusRedirectImgUsemap:Z,focusRedirectLegend:G,focusScrollBody:Y,focusScrollContainerWithoutOverflow:J,focusScrollContainer:X,focusSummary:Q,focusSvgFocusableAttribute:ee,focusSvgTabindexAttribute:te,focusSvgNegativeTabindexAttribute:re,focusSvgUseTabindex:ne,focusSvgForeignobjectTabindex:oe,focusSvg:ie,focusTabindexTrailingCharacters:le,focusTable:se,focusVideoWithoutControls:ue};function executeTests(){var e=detectFocus(fe);Object.keys(de).forEach((function(t){e[t]=de[t]()}));return e}var me=null;function _supports(){if(me){return me}me=j.get();if(!me.time){j.set(executeTests());me=j.get()}return me}var pe=void 0;var be=/^\s*(-|\+)?[0-9]+\s*$/;var ve=/^\s*(-|\+)?[0-9]+.*$/;function isValidTabindex(e){if(!pe){pe=_supports()}var t=pe.focusTabindexTrailingCharacters?ve:be;var r=contextToElement({label:"is/valid-tabindex",resolveDocument:true,context:e});var n=r.hasAttribute("tabindex");var o=r.hasAttribute("tabIndex");if(!n&&!o){return false}var a=r.ownerSVGElement||r.nodeName.toLowerCase()==="svg";if(a&&!pe.focusSvgTabindexAttribute){return false}if(pe.focusInvalidTabindex){return true}var i=r.getAttribute(n?"tabindex":"tabIndex");if(i==="-32768"){return false}return Boolean(i&&t.test(i))}function tabindexValue(e){if(!isValidTabindex(e)){return null}var t=e.hasAttribute("tabindex");var r=t?"tabindex":"tabIndex";var n=parseInt(e.getAttribute(r),10);return isNaN(n)?-1:n}function isUserModifyWritable(e){var t=e.webkitUserModify||"";return Boolean(t&&t.indexOf("write")!==-1)}function hasCssOverflowScroll(e){return[e.getPropertyValue("overflow"),e.getPropertyValue("overflow-x"),e.getPropertyValue("overflow-y")].some((function(e){return e==="auto"||e==="scroll"}))}function hasCssDisplayFlex(e){return e.display.indexOf("flex")>-1}function isScrollableContainer(e,t,r,n){if(t!=="div"&&t!=="span"){return false}if(r&&r!=="div"&&r!=="span"&&!hasCssOverflowScroll(n)){return false}return e.offsetHeight<e.scrollHeight||e.offsetWidth<e.scrollWidth}var ge=void 0;function isFocusRelevantRules(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.except,n=r===undefined?{flexbox:false,scrollable:false,shadow:false}:r;if(!ge){ge=_supports()}var o=contextToElement({label:"is/focus-relevant",resolveDocument:true,context:t});if(!n.shadow&&o.shadowRoot){return true}var a=o.nodeName.toLowerCase();if(a==="input"&&o.type==="hidden"){return false}if(a==="input"||a==="select"||a==="button"||a==="textarea"){return true}if(a==="legend"&&ge.focusRedirectLegend){return true}if(a==="label"){return true}if(a==="area"){return true}if(a==="a"&&o.hasAttribute("href")){return true}if(a==="object"&&o.hasAttribute("usemap")){return false}if(a==="object"){var i=o.getAttribute("type");if(!ge.focusObjectSvg&&i==="image/svg+xml"){return false}else if(!ge.focusObjectSwf&&i==="application/x-shockwave-flash"){return false}}if(a==="iframe"||a==="object"){return true}if(a==="embed"||a==="keygen"){return true}if(o.hasAttribute("contenteditable")){return true}if(a==="audio"&&(ge.focusAudioWithoutControls||o.hasAttribute("controls"))){return true}if(a==="video"&&(ge.focusVideoWithoutControls||o.hasAttribute("controls"))){return true}if(ge.focusSummary&&a==="summary"){return true}var l=isValidTabindex(o);if(a==="img"&&o.hasAttribute("usemap")){return l&&ge.focusImgUsemapTabindex||ge.focusRedirectImgUsemap}if(ge.focusTable&&(a==="table"||a==="td")){return true}if(ge.focusFieldset&&a==="fieldset"){return true}var s=a==="svg";var u=o.ownerSVGElement;var c=o.getAttribute("focusable");var d=tabindexValue(o);if(a==="use"&&d!==null&&!ge.focusSvgUseTabindex){return false}if(a==="foreignobject"){return d!==null&&ge.focusSvgForeignobjectTabindex}if(elementMatches(o,"svg a")&&o.hasAttribute("xlink:href")){return true}if((s||u)&&o.focus&&!ge.focusSvgNegativeTabindexAttribute&&d<0){return false}if(s){return l||ge.focusSvg||ge.focusSvgInIframe||Boolean(ge.focusSvgFocusableAttribute&&c&&c==="true")}if(u){if(ge.focusSvgTabindexAttribute&&l){return true}if(ge.focusSvgFocusableAttribute){return c==="true"}}if(l){return true}var f=window.getComputedStyle(o,null);if(isUserModifyWritable(f)){return true}if(ge.focusImgIsmap&&a==="img"&&o.hasAttribute("ismap")){var m=getParents({context:o}).some((function(e){return e.nodeName.toLowerCase()==="a"&&e.hasAttribute("href")}));if(m){return true}}if(!n.scrollable&&ge.focusScrollContainer){if(ge.focusScrollContainerWithoutOverflow){if(isScrollableContainer(o,a)){return true}}else if(hasCssOverflowScroll(f)){return true}}if(!n.flexbox&&ge.focusFlexboxContainer&&hasCssDisplayFlex(f)){return true}var p=o.parentElement;if(!n.scrollable&&p){var b=p.nodeName.toLowerCase();var v=window.getComputedStyle(p,null);if(ge.focusScrollBody&&isScrollableContainer(p,a,b,v)){return true}if(ge.focusChildrenOfFocusableFlexbox){if(hasCssDisplayFlex(v)){return true}}}return false}isFocusRelevantRules.except=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var t=function isFocusRelevant(t){return isFocusRelevantRules({context:t,except:e})};t.rules=isFocusRelevantRules;return t};var he=isFocusRelevantRules.except({});function findIndex(e,t){if(e.findIndex){return e.findIndex(t)}var r=e.length;if(r===0){return-1}for(var n=0;n<r;n++){if(t(e[n],n,e)){return n}}return-1}function getContentDocument(e){try{return e.contentDocument||e.contentWindow&&e.contentWindow.document||e.getSVGDocument&&e.getSVGDocument()||null}catch(e){return null}}function getWindow(e){var t=getDocument(e);return t.defaultView||window}var ye=void 0;function selectInShadows(e){if(typeof ye!=="string"){var t=cssShadowPiercingDeepCombinator();if(t){ye=", html "+t+" "}}if(!ye){return e}return e+ye+e.replace(/\s*,\s*/g,",").split(",").join(ye)}var xe=void 0;function findDocumentHostElement(e){if(!xe){xe=selectInShadows("object, iframe")}if(e._frameElement!==undefined){return e._frameElement}e._frameElement=null;var t=e.parent.document.querySelectorAll(xe);[].some.call(t,(function(t){var r=getContentDocument(t);if(r!==e.document){return false}e._frameElement=t;return true}));return e._frameElement}function getFrameElement(e){var t=getWindow(e);if(!t.parent||t.parent===t){return null}try{return t.frameElement||findDocumentHostElement(t)}catch(e){return null}}var Ee=/^(area)$/;function computedStyle(e,t){return window.getComputedStyle(e,null).getPropertyValue(t)}function notDisplayed(e){return e.some((function(e){return computedStyle(e,"display")==="none"}))}function notVisible(e){var t=findIndex(e,(function(e){var t=computedStyle(e,"visibility");return t==="hidden"||t==="collapse"}));if(t===-1){return false}var r=findIndex(e,(function(e){return computedStyle(e,"visibility")==="visible"}));if(r===-1){return true}if(t<r){return true}return false}function collapsedParent(e){var t=1;if(e[0].nodeName.toLowerCase()==="summary"){t=2}return e.slice(t).some((function(e){return e.nodeName.toLowerCase()==="details"&&e.open===false}))}function isVisibleRules(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.except,n=r===undefined?{notRendered:false,cssDisplay:false,cssVisibility:false,detailsElement:false,browsingContext:false}:r;var o=contextToElement({label:"is/visible",resolveDocument:true,context:t});var a=o.nodeName.toLowerCase();if(!n.notRendered&&Ee.test(a)){return true}var i=getParents({context:o});var l=a==="audio"&&!o.hasAttribute("controls");if(!n.cssDisplay&&notDisplayed(l?i.slice(1):i)){return false}if(!n.cssVisibility&&notVisible(i)){return false}if(!n.detailsElement&&collapsedParent(i)){return false}if(!n.browsingContext){var s=getFrameElement(o);var u=isVisibleRules.except(n);if(s&&!u(s)){return false}}return true}isVisibleRules.except=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var t=function isVisible(t){return isVisibleRules({context:t,except:e})};t.rules=isVisibleRules;return t};var _e=isVisibleRules.except({});function getMapByName(e,t){var r=t.querySelector('map[name="'+(0,a.default)(e)+'"]');return r||null}function getImageOfArea(e){var t=e.parentElement;if(!t.name||t.nodeName.toLowerCase()!=="map"){return null}var r=getDocument(e);return r.querySelector('img[usemap="#'+(0,a.default)(t.name)+'"]')||null}var we=void 0;function isValidArea(e){if(!we){we=_supports()}var t=contextToElement({label:"is/valid-area",context:e});var r=t.nodeName.toLowerCase();if(r!=="area"){return false}var n=t.hasAttribute("tabindex");if(!we.focusAreaTabindex&&n){return false}var o=getImageOfArea(t);if(!o||!_e(o)){return false}if(!we.focusBrokenImageMap&&(!o.complete||!o.naturalHeight||o.offsetWidth<=0||o.offsetHeight<=0)){return false}if(!we.focusAreaWithoutHref&&!t.href){return we.focusAreaTabindex&&n||we.focusAreaImgTabindex&&o.hasAttribute("tabindex")}var a=getParents({context:o}).slice(1).some((function(e){var t=e.nodeName.toLowerCase();return t==="button"||t==="a"}));if(a){return false}return true}var Oe=void 0;var je=void 0;var ke={input:true,select:true,textarea:true,button:true,fieldset:true,form:true};function isNativeDisabledSupported(e){if(!Oe){Oe=_supports();if(Oe.focusFieldsetDisabled){delete ke.fieldset}if(Oe.focusFormDisabled){delete ke.form}je=new RegExp("^("+Object.keys(ke).join("|")+")$")}var t=contextToElement({label:"is/native-disabled-supported",context:e});var r=t.nodeName.toLowerCase();return Boolean(je.test(r))}var Se=void 0;function isDisabledFieldset(e){var t=e.nodeName.toLowerCase();return t==="fieldset"&&e.disabled}function isDisabledForm(e){var t=e.nodeName.toLowerCase();return t==="form"&&e.disabled}function isDisabled(e){if(!Se){Se=_supports()}var t=contextToElement({label:"is/disabled",context:e});if(t.hasAttribute("data-ally-disabled")){return true}if(!isNativeDisabledSupported(t)){return false}if(t.disabled){return true}var r=getParents({context:t});if(r.some(isDisabledFieldset)){return true}if(!Se.focusFormDisabled&&r.some(isDisabledForm)){return true}return false}function isOnlyTabbableRules(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.except,n=r===undefined?{onlyFocusableBrowsingContext:false,visible:false}:r;var o=contextToElement({label:"is/only-tabbable",resolveDocument:true,context:t});if(!n.visible&&!_e(o)){return false}if(!n.onlyFocusableBrowsingContext&&(s.is.GECKO||s.is.TRIDENT||s.is.EDGE)){var a=getFrameElement(o);if(a){if(tabindexValue(a)<0){return false}}}var i=o.nodeName.toLowerCase();var l=tabindexValue(o);if(i==="label"&&s.is.GECKO){return l!==null&&l>=0}if(s.is.GECKO&&o.ownerSVGElement&&!o.focus){if(i==="a"&&o.hasAttribute("xlink:href")){if(s.is.GECKO){return true}}}return false}isOnlyTabbableRules.except=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var t=function isOnlyTabbable(t){return isOnlyTabbableRules({context:t,except:e})};t.rules=isOnlyTabbableRules;return t};var Te=isOnlyTabbableRules.except({});var Ce=void 0;function isOnlyFocusRelevant(e){var t=e.nodeName.toLowerCase();if(t==="embed"||t==="keygen"){return true}var r=tabindexValue(e);if(e.shadowRoot&&r===null){return true}if(t==="label"){return!Ce.focusLabelTabindex||r===null}if(t==="legend"){return r===null}if(Ce.focusSvgFocusableAttribute&&(e.ownerSVGElement||t==="svg")){var n=e.getAttribute("focusable");return n&&n==="false"}if(t==="img"&&e.hasAttribute("usemap")){return r===null||!Ce.focusImgUsemapTabindex}if(t==="area"){return!isValidArea(e)}return false}function isFocusableRules(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.except,n=r===undefined?{disabled:false,visible:false,onlyTabbable:false}:r;if(!Ce){Ce=_supports()}var o=Te.rules.except({onlyFocusableBrowsingContext:true,visible:n.visible});var a=contextToElement({label:"is/focusable",resolveDocument:true,context:t});var i=he.rules({context:a,except:n});if(!i||isOnlyFocusRelevant(a)){return false}if(!n.disabled&&isDisabled(a)){return false}if(!n.onlyTabbable&&o(a)){return false}if(!n.visible){var l={context:a,except:{}};if(Ce.focusInHiddenIframe){l.except.browsingContext=true}if(Ce.focusObjectSvgHidden){var s=a.nodeName.toLowerCase();if(s==="object"){l.except.cssVisibility=true}}if(!_e.rules(l)){return false}}var u=getFrameElement(a);if(u){var c=u.nodeName.toLowerCase();if(c==="object"&&!Ce.focusInZeroDimensionObject){if(!u.offsetWidth||!u.offsetHeight){return false}}}var d=a.nodeName.toLowerCase();if(d==="svg"&&Ce.focusSvgInIframe&&!u&&a.getAttribute("tabindex")===null){return false}return true}isFocusableRules.except=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var t=function isFocusable(t){return isFocusableRules({context:t,except:e})};t.rules=isFocusableRules;return t};var De=isFocusableRules.except({});function createFilter(e){var t=function filter(t){if(t.shadowRoot){return NodeFilter.FILTER_ACCEPT}if(e(t)){return NodeFilter.FILTER_ACCEPT}return NodeFilter.FILTER_SKIP};t.acceptNode=t;return t}var Pe=createFilter(he);function queryFocusableStrict(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.includeContext,n=e.includeOnlyTabbable,o=e.strategy;if(!t){t=document.documentElement}var a=De.rules.except({onlyTabbable:n});var i=getDocument(t);var l=i.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,o==="all"?Pe:createFilter(a),false);var s=[];while(l.nextNode()){if(l.currentNode.shadowRoot){if(a(l.currentNode)){s.push(l.currentNode)}s=s.concat(queryFocusableStrict({context:l.currentNode.shadowRoot,includeOnlyTabbable:n,strategy:o}))}else{s.push(l.currentNode)}}if(r){if(o==="all"){if(he(t)){s.unshift(t)}}else if(a(t)){s.unshift(t)}}return s}var Me=void 0;var Ne=void 0;function selector$2(){if(!Me){Me=_supports()}if(typeof Ne==="string"){return Ne}Ne=""+(Me.focusTable?"table, td,":"")+(Me.focusFieldset?"fieldset,":"")+"svg a,"+"a[href],"+"area[href],"+"input, select, textarea, button,"+"iframe, object, embed,"+"keygen,"+(Me.focusAudioWithoutControls?"audio,":"audio[controls],")+(Me.focusVideoWithoutControls?"video,":"video[controls],")+(Me.focusSummary?"summary,":"")+"[tabindex],"+"[contenteditable]";Ne=selectInShadows(Ne);return Ne}function queryFocusableQuick(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.includeContext,n=e.includeOnlyTabbable;var o=selector$2();var a=t.querySelectorAll(o);var i=De.rules.except({onlyTabbable:n});var l=[].filter.call(a,i);if(r&&i(t)){l.unshift(t)}return l}function queryFocusable(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.includeContext,n=e.includeOnlyTabbable,o=e.strategy,a=o===undefined?"quick":o;var i=contextToElement({label:"query/focusable",resolveDocument:true,defaultToDocument:true,context:t});var l={context:i,includeContext:r,includeOnlyTabbable:n,strategy:a};if(a==="quick"){return queryFocusableQuick(l)}else if(a==="strict"||a==="all"){return queryFocusableStrict(l)}throw new TypeError('query/focusable requires option.strategy to be one of ["quick", "strict", "all"]')}var Ae=void 0;var Re=/^(fieldset|table|td|body)$/;function isTabbableRules(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.except,n=r===undefined?{flexbox:false,scrollable:false,shadow:false,visible:false,onlyTabbable:false}:r;if(!Ae){Ae=_supports()}var o=contextToElement({label:"is/tabbable",resolveDocument:true,context:t});if(s.is.BLINK&&s.is.ANDROID&&s.majorVersion>42){return false}var a=getFrameElement(o);if(a){if(s.is.WEBKIT&&s.is.IOS){return false}if(tabindexValue(a)<0){return false}if(!n.visible&&(s.is.BLINK||s.is.WEBKIT)&&!_e(a)){return false}var i=a.nodeName.toLowerCase();if(i==="object"){var l=s.name==="Chrome"&&s.majorVersion>=54||s.name==="Opera"&&s.majorVersion>=41;if(s.is.WEBKIT||s.is.BLINK&&!l){return false}}}var u=o.nodeName.toLowerCase();var c=tabindexValue(o);var d=c===null?null:c>=0;if(s.is.EDGE&&s.majorVersion>=14&&a&&o.ownerSVGElement&&c<0){return true}var f=d!==false;var m=c!==null&&c>=0;if(o.hasAttribute("contenteditable")){return f}if(Re.test(u)&&d!==true){return false}if(s.is.WEBKIT&&s.is.IOS){var p=u==="input"&&o.type==="text"||o.type==="password"||u==="select"||u==="textarea"||o.hasAttribute("contenteditable");if(!p){var b=window.getComputedStyle(o,null);p=isUserModifyWritable(b)}if(!p){return false}}if(u==="use"&&c!==null){if(s.is.BLINK||s.is.WEBKIT&&s.majorVersion===9){return true}}if(elementMatches(o,"svg a")&&o.hasAttribute("xlink:href")){if(f){return true}if(o.focus&&!Ae.focusSvgNegativeTabindexAttribute){return true}}if(u==="svg"&&Ae.focusSvgInIframe&&f){return true}if(s.is.TRIDENT||s.is.EDGE){if(u==="svg"){if(Ae.focusSvg){return true}return o.hasAttribute("focusable")||m}if(o.ownerSVGElement){if(Ae.focusSvgTabindexAttribute&&m){return true}return o.hasAttribute("focusable")}}if(o.tabIndex===undefined){return Boolean(n.onlyTabbable)}if(u==="audio"){if(!o.hasAttribute("controls")){return false}else if(s.is.BLINK){return true}}if(u==="video"){if(!o.hasAttribute("controls")){if(s.is.TRIDENT||s.is.EDGE){return false}}else if(s.is.BLINK||s.is.GECKO){return true}}if(u==="object"){if(s.is.BLINK||s.is.WEBKIT){return false}}if(u==="iframe"){return false}if(!n.scrollable&&s.is.GECKO){var v=window.getComputedStyle(o,null);if(hasCssOverflowScroll(v)){return f}}if(s.is.TRIDENT||s.is.EDGE){if(u==="area"){var g=getImageOfArea(o);if(g&&tabindexValue(g)<0){return false}}var h=window.getComputedStyle(o,null);if(isUserModifyWritable(h)){return o.tabIndex>=0}if(!n.flexbox&&hasCssDisplayFlex(h)){if(c!==null){return m}return Fe(o)&&Ie(o)}if(isScrollableContainer(o,u)){return false}var y=o.parentElement;if(y){var x=y.nodeName.toLowerCase();var E=window.getComputedStyle(y,null);if(isScrollableContainer(y,u,x,E)){return false}if(hasCssDisplayFlex(E)){return m}}}return o.tabIndex>=0}isTabbableRules.except=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var t=function isTabbable(t){return isTabbableRules({context:t,except:e})};t.rules=isTabbableRules;return t};var Fe=he.rules.except({flexbox:true});var Ie=isTabbableRules.except({flexbox:true});var Le=isTabbableRules.except({});function queryTabbable(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.includeContext,n=e.includeOnlyTabbable,o=e.strategy;var a=Le.rules.except({onlyTabbable:n});return queryFocusable({context:t,includeContext:r,includeOnlyTabbable:n,strategy:o}).filter(a)}function compareDomPosition(e,t){return e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING?-1:1}function sortDomOrder(e){return e.sort(compareDomPosition)}function getFirstSuccessorOffset(e,t){return findIndex(e,(function(e){return t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING}))}function findInsertionOffsets(e,t,r){var n=[];t.forEach((function(t){var o=true;var a=e.indexOf(t);if(a===-1){a=getFirstSuccessorOffset(e,t);o=false}if(a===-1){a=e.length}var i=nodeArray(r?r(t):t);if(!i.length){return}n.push({offset:a,replace:o,elements:i})}));return n}function insertElementsAtOffsets(e,t){var r=0;t.sort((function(e,t){return e.offset-t.offset}));t.forEach((function(t){var n=t.replace?1:0;var o=[t.offset+r,n].concat(t.elements);e.splice.apply(e,o);r+=t.elements.length-n}))}function mergeInDomOrder(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.list,r=e.elements,n=e.resolveElement;var o=t.slice(0);var a=nodeArray(r).slice(0);sortDomOrder(a);var i=findInsertionOffsets(o,a,n);insertElementsAtOffsets(o,i);return o}var Be=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,r){if(t)defineProperties(e.prototype,t);if(r)defineProperties(e,r);return e}}();function _classCallCheck(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var He=function(){function Maps(e){_classCallCheck(this,Maps);this._document=getDocument(e);this.maps={}}Be(Maps,[{key:"getAreasFor",value:function getAreasFor(e){if(!this.maps[e]){this.addMapByName(e)}return this.maps[e]}},{key:"addMapByName",value:function addMapByName(e){var t=getMapByName(e,this._document);if(!t){return}this.maps[t.name]=queryTabbable({context:t})}},{key:"extractAreasFromList",value:function extractAreasFromList(e){return e.filter((function(e){var t=e.nodeName.toLowerCase();if(t!=="area"){return true}var r=e.parentNode;if(!this.maps[r.name]){this.maps[r.name]=[]}this.maps[r.name].push(e);return false}),this)}}]);return Maps}();function sortArea(e,t){var r=t.querySelectorAll("img[usemap]");var n=new He(t);var o=n.extractAreasFromList(e);if(!r.length){return o}return mergeInDomOrder({list:o,elements:r,resolveElement:function resolveElement(e){var t=e.getAttribute("usemap").slice(1);return n.getAreasFor(t)}})}var ze=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,r){if(t)defineProperties(e.prototype,t);if(r)defineProperties(e,r);return e}}();function _classCallCheck$1(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var qe=function(){function Shadows(e,t){_classCallCheck$1(this,Shadows);this.context=e;this.sortElements=t;this.hostCounter=1;this.inHost={};this.inDocument=[];this.hosts={};this.elements={}}ze(Shadows,[{key:"_registerHost",value:function _registerHost(e){if(e._sortingId){return}e._sortingId="shadow-"+this.hostCounter++;this.hosts[e._sortingId]=e;var t=getShadowHost({context:e});if(t){this._registerHost(t);this._registerHostParent(e,t)}else{this.inDocument.push(e)}}},{key:"_registerHostParent",value:function _registerHostParent(e,t){if(!this.inHost[t._sortingId]){this.inHost[t._sortingId]=[]}this.inHost[t._sortingId].push(e)}},{key:"_registerElement",value:function _registerElement(e,t){if(!this.elements[t._sortingId]){this.elements[t._sortingId]=[]}this.elements[t._sortingId].push(e)}},{key:"extractElements",value:function extractElements(e){return e.filter((function(e){var t=getShadowHost({context:e});if(!t){return true}this._registerHost(t);this._registerElement(e,t);return false}),this)}},{key:"sort",value:function sort(e){var t=this._injectHosts(e);t=this._replaceHosts(t);this._cleanup();return t}},{key:"_injectHosts",value:function _injectHosts(e){Object.keys(this.hosts).forEach((function(e){var t=this.elements[e];var r=this.inHost[e];var n=this.hosts[e].shadowRoot;this.elements[e]=this._merge(t,r,n)}),this);return this._merge(e,this.inDocument,this.context)}},{key:"_merge",value:function _merge(e,t,r){var n=mergeInDomOrder({list:e,elements:t});return this.sortElements(n,r)}},{key:"_replaceHosts",value:function _replaceHosts(e){return mergeInDomOrder({list:e,elements:this.inDocument,resolveElement:this._resolveHostElement.bind(this)})}},{key:"_resolveHostElement",value:function _resolveHostElement(e){var t=mergeInDomOrder({list:this.elements[e._sortingId],elements:this.inHost[e._sortingId],resolveElement:this._resolveHostElement.bind(this)});var r=tabindexValue(e);if(r!==null&&r>-1){return[e].concat(t)}return t}},{key:"_cleanup",value:function _cleanup(){Object.keys(this.hosts).forEach((function(e){delete this.hosts[e]._sortingId}),this)}}]);return Shadows}();function sortShadowed(e,t,r){var n=new qe(t,r);var o=n.extractElements(e);if(o.length===e.length){return r(e)}return n.sort(o)}function sortTabindex(e){var t={};var r=[];var n=e.filter((function(e){var n=e.tabIndex;if(n===undefined){n=tabindexValue(e)}if(n<=0||n===null||n===undefined){return true}if(!t[n]){t[n]=[];r.push(n)}t[n].push(e);return false}));var o=r.sort().map((function(e){return t[e]})).reduceRight((function(e,t){return t.concat(e)}),n);return o}var We=void 0;function moveContextToBeginning(e,t){var r=e.indexOf(t);if(r>0){var n=e.splice(r,1);return n.concat(e)}return e}function sortElements(e,t){if(We.tabsequenceAreaAtImgPosition){e=sortArea(e,t)}e=sortTabindex(e);return e}function queryTabsequence(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.context,r=e.includeContext,n=e.includeOnlyTabbable,o=e.strategy;if(!We){We=_supports()}var a=nodeArray(t)[0]||document.documentElement;var i=queryTabbable({context:a,includeContext:r,includeOnlyTabbable:n,strategy:o});if(document.body.createShadowRoot&&s.is.BLINK){i=sortShadowed(i,a,sortElements)}else{i=sortElements(i,a)}if(r){i=moveContextToBeginning(i,a)}return i}var Ue={tab:9,left:37,up:38,right:39,down:40,pageUp:33,"page-up":33,pageDown:34,"page-down":34,end:35,home:36,enter:13,escape:27,space:32,shift:16,capsLock:20,"caps-lock":20,ctrl:17,alt:18,meta:91,pause:19,insert:45,delete:46,backspace:8,_alias:{91:[92,93,224]}};for(var Ve=1;Ve<26;Ve++){Ue["f"+Ve]=Ve+111}for(var $e=0;$e<10;$e++){var Ke=$e+48;var Ze=$e+96;Ue[$e]=Ke;Ue["num-"+$e]=Ze;Ue._alias[Ke]=[Ze]}for(var Ge=0;Ge<26;Ge++){var Ye=Ge+65;var Je=String.fromCharCode(Ye).toLowerCase();Ue[Je]=Ye}var Xe={alt:"altKey",ctrl:"ctrlKey",meta:"metaKey",shift:"shiftKey"};var Qe=Object.keys(Xe).map((function(e){return Xe[e]}));function createExpectedModifiers(e){var t=e?null:false;return{altKey:t,ctrlKey:t,metaKey:t,shiftKey:t}}function resolveModifiers(e){var t=e.indexOf("*")!==-1;var r=createExpectedModifiers(t);e.forEach((function(e){if(e==="*"){return}var t=true;var n=e.slice(0,1);if(n==="?"){t=null}else if(n==="!"){t=false}if(t!==true){e=e.slice(1)}var o=Xe[e];if(!o){throw new TypeError('Unknown modifier "'+e+'"')}r[o]=t}));return r}function resolveKey(e){var t=Ue[e]||parseInt(e,10);if(!t||typeof t!=="number"||isNaN(t)){throw new TypeError('Unknown key "'+e+'"')}return[t].concat(Ue._alias[t]||[])}function matchModifiers(e,t){return!Qe.some((function(r){return typeof e[r]==="boolean"&&Boolean(t[r])!==e[r]}))}function keyBinding(e){return e.split(/\s+/).map((function(e){var t=e.split("+");var r=resolveModifiers(t.slice(0,-1));var n=resolveKey(t.slice(-1));return{keyCodes:n,modifiers:r,matchModifiers:matchModifiers.bind(null,r)}}))}function getParentComparator(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},t=e.parent,r=e.element,n=e.includeSelf;if(t){return function isChildOf(e){return Boolean(n&&e===t||t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)}}else if(r){return function isParentOf(e){return Boolean(n&&r===e||e.compareDocumentPosition(r)&Node.DOCUMENT_POSITION_CONTAINED_BY)}}throw new TypeError("util/compare-position#getParentComparator required either options.parent or options.element")}function whenKey(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var t={};var r=nodeArray(e.context)[0]||document.documentElement;delete e.context;var n=nodeArray(e.filter);delete e.filter;var o=Object.keys(e);if(!o.length){throw new TypeError("when/key requires at least one option key")}var a=function registerBinding(e){e.keyCodes.forEach((function(r){if(!t[r]){t[r]=[]}t[r].push(e)}))};o.forEach((function(t){if(typeof e[t]!=="function"){throw new TypeError('when/key requires option["'+t+'"] to be a function')}var r=function addCallback(r){r.callback=e[t];return r};keyBinding(t).map(r).forEach(a)}));var i=function handleKeyDown(e){if(e.defaultPrevented){return}if(n.length){var o=getParentComparator({element:e.target,includeSelf:true});if(n.some(o)){return}}var a=e.keyCode||e.which;if(!t[a]){return}t[a].forEach((function(t){if(!t.matchModifiers(e)){return}t.callback.call(r,e,l)}))};r.addEventListener("keydown",i,false);var l=function disengage(){r.removeEventListener("keydown",i,false)};return{disengage:l}}function default_1({context:e}={}){if(!e){e=document.documentElement}queryTabsequence();return whenKey({"?alt+?shift+tab":function altShiftTab(t){t.preventDefault();var r=queryTabsequence({context:e});var n=t.shiftKey;var o=r[0];var a=r[r.length-1];var i=n?o:a;var l=n?a:o;if(isActiveElement(i)){l.focus();return}var s=void 0;var u=r.some((function(e,t){if(!isActiveElement(e)){return false}s=t;return true}));if(!u){o.focus();return}var c=n?-1:1;r[s+c].focus()}})}t["default"]=default_1},993:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=void 0;const n=r(910);const o=(0,n.noop)`
  [data-nextjs-dialog-overlay] {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    z-index: 9000;

    display: flex;
    align-content: center;
    align-items: center;
    flex-direction: column;
    padding: 10vh 15px 0;
  }

  @media (max-height: 812px) {
    [data-nextjs-dialog-overlay] {
      padding: 15px 15px 0;
    }
  }

  [data-nextjs-dialog-backdrop] {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--color-backdrop);
    pointer-events: all;
    z-index: -1;
  }

  [data-nextjs-dialog-backdrop-fixed] {
    cursor: not-allowed;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
  }
`;t.styles=o},338:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.ShadowPortal=void 0;const i=a(r(522));const l=r(255);const s=function Portal({children:e,globalOverlay:t}){let r=i.useRef(null);let n=i.useRef(null);let o=i.useRef(null);let[,a]=i.useState();i.useLayoutEffect((()=>{const e=t?document:r.current.ownerDocument;n.current=e.createElement("nextjs-portal");o.current=n.current.attachShadow({mode:"open"});e.body.appendChild(n.current);a({});return()=>{if(n.current&&n.current.ownerDocument){n.current.ownerDocument.body.removeChild(n.current)}}}),[t]);return o.current?(0,l.createPortal)(e,o.current):t?null:i.createElement("span",{ref:r})};t.ShadowPortal=s},215:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.Terminal=void 0;const l=i(r(997));const s=a(r(522));const u=function Terminal({content:e}){const t=s.useMemo((()=>l.default.ansiToJson(e,{json:true,use_classes:true,remove_empty:true})),[e]);return s.createElement("div",{"data-nextjs-terminal":true},s.createElement("pre",null,t.map(((e,t)=>s.createElement("span",{key:`terminal-entry-${t}`,style:{color:e.fg?`var(--color-${e.fg})`:undefined,...e.decoration==="bold"?{fontWeight:800}:e.decoration==="italic"?{fontStyle:"italic"}:undefined}},e.content)))))};t.Terminal=u},236:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.Terminal=void 0;var n=r(215);Object.defineProperty(t,"Terminal",{enumerable:true,get:function(){return n.Terminal}})},488:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=void 0;const n=r(910);const o=(0,n.noop)`
  [data-nextjs-terminal] {
    border-radius: var(--size-gap-half);
    background-color: var(--color-ansi-bg);
    color: var(--color-ansi-fg);
  }
  [data-nextjs-terminal]::selection,
  [data-nextjs-terminal] *::selection {
    background-color: var(--color-ansi-selection);
  }
  [data-nextjs-terminal] * {
    color: inherit;
    background-color: transparent;
    font-family: var(--font-stack-monospace);
  }
  [data-nextjs-terminal] > * {
    margin: 0;
    padding: calc(var(--size-gap) + var(--size-gap-half))
      calc(var(--size-gap-double) + var(--size-gap-half));
  }

  [data-nextjs-terminal] pre {
    white-space: pre-wrap;
    word-break: break-word;
  }
`;t.styles=o},683:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.Toast=void 0;const i=a(r(522));const l=function Toast({onClick:e,children:t,className:r}){return i.createElement("div",{"data-nextjs-toast":true,onClick:e,className:r},i.createElement("div",{"data-nextjs-toast-wrapper":true},t))};t.Toast=l},120:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.Toast=t.styles=void 0;var n=r(69);Object.defineProperty(t,"styles",{enumerable:true,get:function(){return n.styles}});var o=r(683);Object.defineProperty(t,"Toast",{enumerable:true,get:function(){return o.Toast}})},69:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.styles=void 0;const n=r(910);const o=(0,n.noop)`
  [data-nextjs-toast] {
    position: fixed;
    bottom: var(--size-gap-double);
    left: var(--size-gap-double);
    max-width: 420px;
    z-index: 9000;
  }

  @media (max-width: 440px) {
    [data-nextjs-toast] {
      max-width: 90vw;
      left: 5vw;
    }
  }

  [data-nextjs-toast-wrapper] {
    padding: 16px;
    border-radius: var(--size-gap-half);
    font-weight: 500;
    color: var(--color-ansi-bright-white);
    background-color: var(--color-ansi-red);
    box-shadow: 0px var(--size-gap-double) var(--size-gap-quad)
      rgba(0, 0, 0, 0.25);
  }
`;t.styles=o},936:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.styles=t.BuildError=void 0;const i=a(r(522));const l=r(651);const s=r(278);const u=r(236);const c=r(910);const d=function BuildError({message:e}){const t=i.useCallback((()=>{}),[]);return i.createElement(s.Overlay,{fixed:true},i.createElement(l.Dialog,{type:"error","aria-labelledby":"nextjs__container_build_error_label","aria-describedby":"nextjs__container_build_error_desc",onClose:t},i.createElement(l.DialogContent,null,i.createElement(l.DialogHeader,{className:"nextjs-container-build-error-header"},i.createElement("h4",{id:"nextjs__container_build_error_label"},"Failed to compile")),i.createElement(l.DialogBody,{className:"nextjs-container-build-error-body"},i.createElement(u.Terminal,{content:e}),i.createElement("footer",null,i.createElement("p",{id:"nextjs__container_build_error_desc"},i.createElement("small",null,"This error occurred during the build process and can only be dismissed by fixing the error.")))))))};t.BuildError=d;t.styles=(0,c.noop)`
  .nextjs-container-build-error-header > h4 {
    line-height: 1.5;
    margin: 0;
    padding: 0;
  }

  .nextjs-container-build-error-body footer {
    margin-top: var(--size-gap);
  }
  .nextjs-container-build-error-body footer p {
    margin: 0;
  }

  .nextjs-container-build-error-body small {
    color: var(--color-font);
  }
`},355:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.styles=t.Errors=void 0;const i=a(r(522));const l=r(851);const s=r(651);const u=r(732);const c=r(278);const d=r(120);const f=r(403);const m=r(233);const p=r(910);const b=r(865);const v=r(484);function getErrorSignature(e){const{event:t}=e;switch(t.type){case l.TYPE_UNHANDLED_ERROR:case l.TYPE_UNHANDLED_REJECTION:{return`${t.reason.name}::${t.reason.message}::${t.reason.stack}`}default:{}}const r=t;return""}const g=function HotlinkedText(e){const{text:t}=e;const r=/https?:\/\/[^\s/$.?#].[^\s)'"]*/i;return i.createElement(i.Fragment,null,r.test(t)?t.split(" ").map(((e,t,n)=>{if(r.test(e)){const o=r.exec(e);return i.createElement(i.Fragment,{key:`link-${t}`},o&&i.createElement("a",{href:o[0],target:"_blank",rel:"noreferrer noopener"},e),t===n.length-1?"":" ")}return t===n.length-1?i.createElement(i.Fragment,{key:`text-${t}`},e):i.createElement(i.Fragment,{key:`text-${t}`},e," ")})):t)};const h=function Errors({errors:e}){const[t,r]=i.useState({});const[n,o]=i.useMemo((()=>{let r=[];let n=null;for(let o=0;o<e.length;++o){const a=e[o];const{id:i}=a;if(i in t){r.push(t[i]);continue}if(o>0){const t=e[o-1];if(getErrorSignature(t)===getErrorSignature(a)){continue}}n=a;break}return[r,n]}),[e,t]);const a=i.useMemo((()=>n.length<1&&Boolean(e.length)),[e.length,n.length]);i.useEffect((()=>{if(o==null){return}let e=true;(0,f.getErrorByType)(o).then((t=>{if(e){r((e=>({...e,[t.id]:t})))}}),(()=>{}));return()=>{e=false}}),[o]);const[l,p]=i.useState("fullscreen");const[h,y]=i.useState(0);const x=i.useCallback((e=>{e?.preventDefault();y((e=>Math.max(0,e-1)))}),[]);const E=i.useCallback((e=>{e?.preventDefault();y((e=>Math.max(0,Math.min(n.length-1,e+1))))}),[n.length]);const _=i.useMemo((()=>n[h]??null),[h,n]);i.useEffect((()=>{if(e.length<1){r({});p("hidden");y(0)}}),[e.length]);const w=i.useCallback((e=>{e?.preventDefault();p("minimized")}),[]);const O=i.useCallback((e=>{e?.preventDefault();p("hidden")}),[]);const j=i.useCallback((e=>{e?.preventDefault();p("fullscreen")}),[]);if(e.length<1||_==null){return null}if(a){return i.createElement(c.Overlay,null)}if(l==="hidden"){return null}if(l==="minimized"){return i.createElement(d.Toast,{className:"nextjs-toast-errors-parent",onClick:j},i.createElement("div",{className:"nextjs-toast-errors"},i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i.createElement("circle",{cx:"12",cy:"12",r:"10"}),i.createElement("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),i.createElement("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})),i.createElement("span",null,n.length," error",n.length>1?"s":""),i.createElement("button",{"data-nextjs-toast-errors-hide-button":true,className:"nextjs-toast-errors-hide-button",type:"button",onClick:e=>{e.stopPropagation();O()},"aria-label":"Hide Errors"},i.createElement(b.CloseIcon,null))))}const k=["server","edge-server"].includes((0,m.getErrorSource)(_.error)||"");return i.createElement(c.Overlay,null,i.createElement(s.Dialog,{type:"error","aria-labelledby":"nextjs__container_errors_label","aria-describedby":"nextjs__container_errors_desc",onClose:k?undefined:w},i.createElement(s.DialogContent,null,i.createElement(s.DialogHeader,{className:"nextjs-container-errors-header"},i.createElement(u.LeftRightDialogHeader,{previous:h>0?x:null,next:h<n.length-1?E:null,close:k?undefined:w},i.createElement("small",null,i.createElement("span",null,h+1)," of"," ",i.createElement("span",null,n.length)," unhandled error",n.length<2?"":"s")),i.createElement("h1",{id:"nextjs__container_errors_label"},k?"Server Error":"Unhandled Runtime Error"),i.createElement("p",{id:"nextjs__container_errors_desc"},_.error.name,":"," ",i.createElement(g,{text:_.error.message})),k?i.createElement("div",null,i.createElement("small",null,"This error happened while generating the page. Any console logs will be displayed in the terminal window.")):undefined),i.createElement(s.DialogBody,{className:"nextjs-container-errors-body"},i.createElement(v.RuntimeError,{key:_.id.toString(),error:_})))))};t.Errors=h;t.styles=(0,p.noop)`
  .nextjs-container-errors-header > h1 {
    font-size: var(--size-font-big);
    line-height: var(--size-font-bigger);
    font-weight: bold;
    margin: 0;
    margin-top: calc(var(--size-gap-double) + var(--size-gap-half));
  }
  .nextjs-container-errors-header small {
    font-size: var(--size-font-small);
    color: var(--color-accents-1);
    margin-left: var(--size-gap-double);
  }
  .nextjs-container-errors-header small > span {
    font-family: var(--font-stack-monospace);
  }
  .nextjs-container-errors-header > p {
    font-family: var(--font-stack-monospace);
    font-size: var(--size-font-small);
    line-height: var(--size-font-big);
    font-weight: bold;
    margin: 0;
    margin-top: var(--size-gap-half);
    color: var(--color-ansi-red);
    white-space: pre-wrap;
  }
  .nextjs-container-errors-header > div > small {
    margin: 0;
    margin-top: var(--size-gap-half);
  }
  .nextjs-container-errors-header > p > a {
    color: var(--color-ansi-red);
  }

  .nextjs-container-errors-body > h2:not(:first-child) {
    margin-top: calc(var(--size-gap-double) + var(--size-gap));
  }
  .nextjs-container-errors-body > h2 {
    margin-bottom: var(--size-gap);
    font-size: var(--size-font-big);
  }

  .nextjs-toast-errors-parent {
    cursor: pointer;
    transition: transform 0.2s ease;
  }
  .nextjs-toast-errors-parent:hover {
    transform: scale(1.1);
  }
  .nextjs-toast-errors {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .nextjs-toast-errors > svg {
    margin-right: var(--size-gap);
  }
  .nextjs-toast-errors-hide-button {
    margin-left: var(--size-gap-triple);
    border: none;
    background: none;
    color: var(--color-ansi-bright-white);
    padding: 0;
    transition: opacity 0.25s ease;
    opacity: 0.7;
  }
  .nextjs-toast-errors-hide-button:hover {
    opacity: 1;
  }
`},484:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.RuntimeError=t.styles=void 0;const i=a(r(522));const l=r(413);const s=r(910);const u=r(504);const c=function CallStackFrame({frame:e}){const t=e.originalStackFrame??e.sourceStackFrame;const r=Boolean(e.originalCodeFrame);const n=i.useCallback((()=>{if(!r)return;const e=new URLSearchParams;for(const r in t){e.append(r,(t[r]??"").toString())}self.fetch(`${process.env.__NEXT_ROUTER_BASEPATH||""}/__nextjs_launch-editor?${e.toString()}`).then((()=>{}),(()=>{console.error("There was an issue opening this code in your editor.")}))}),[r,t]);return i.createElement("div",{"data-nextjs-call-stack-frame":true},i.createElement("h3",{"data-nextjs-frame-expanded":Boolean(e.expanded)},t.methodName),i.createElement("div",{"data-has-source":r?"true":undefined,tabIndex:r?10:undefined,role:r?"link":undefined,onClick:n,title:r?"Click to open in your editor":undefined},i.createElement("span",null,(0,u.getFrameSource)(t)),i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i.createElement("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),i.createElement("polyline",{points:"15 3 21 3 21 9"}),i.createElement("line",{x1:"10",y1:"14",x2:"21",y2:"3"}))))};const d=function RuntimeError({error:e}){const t=i.useMemo((()=>e.frames.findIndex((e=>e.expanded&&Boolean(e.originalCodeFrame)&&Boolean(e.originalStackFrame)))),[e.frames]);const r=i.useMemo((()=>e.frames[t]??null),[e.frames,t]);const n=i.useMemo((()=>t<0?[]:e.frames.slice(0,t)),[e.frames,t]);const[o,a]=i.useState(r==null);const s=i.useCallback((()=>{a((e=>!e))}),[]);const u=i.useMemo((()=>n.filter((e=>e.expanded||o))),[o,n]);const d=i.useMemo((()=>e.frames.slice(t+1)),[e.frames,t]);const f=i.useMemo((()=>d.filter((e=>e.expanded||o))),[o,d]);const m=i.useMemo((()=>d.length!==f.length||o&&r!=null),[o,d.length,r,f.length]);return i.createElement(i.Fragment,null,r?i.createElement(i.Fragment,null,i.createElement("h2",null,"Source"),u.map(((e,t)=>i.createElement(c,{key:`leading-frame-${t}-${o}`,frame:e}))),i.createElement(l.CodeFrame,{stackFrame:r.originalStackFrame,codeFrame:r.originalCodeFrame})):undefined,e.componentStack?i.createElement(i.Fragment,null,i.createElement("h2",null,"Component Stack"),e.componentStack.map(((e,t)=>i.createElement("div",{key:t,"data-nextjs-component-stack-frame":true},i.createElement("h3",null,e))))):null,f.length?i.createElement(i.Fragment,null,i.createElement("h2",null,"Call Stack"),f.map(((e,t)=>i.createElement(c,{key:`call-stack-${t}-${o}`,frame:e})))):undefined,m?i.createElement(i.Fragment,null,i.createElement("button",{tabIndex:10,"data-nextjs-data-runtime-error-collapsed-action":true,type:"button",onClick:s},o?"Hide":"Show"," collapsed frames")):undefined)};t.RuntimeError=d;t.styles=(0,s.noop)`
  button[data-nextjs-data-runtime-error-collapsed-action] {
    background: none;
    border: none;
    padding: 0;
    font-size: var(--size-font-small);
    line-height: var(--size-font-bigger);
    color: var(--color-accents-3);
  }

  [data-nextjs-call-stack-frame]:not(:last-child),
  [data-nextjs-component-stack-frame]:not(:last-child) {
    margin-bottom: var(--size-gap-double);
  }

  [data-nextjs-call-stack-frame] > h3,
  [data-nextjs-component-stack-frame] > h3 {
    margin-top: 0;
    margin-bottom: var(--size-gap);
    font-family: var(--font-stack-monospace);
    color: var(--color-stack-h6);
  }
  [data-nextjs-call-stack-frame] > h3[data-nextjs-frame-expanded='false'] {
    color: var(--color-stack-headline);
  }
  [data-nextjs-call-stack-frame] > div {
    display: flex;
    align-items: center;
    padding-left: calc(var(--size-gap) + var(--size-gap-half));
    font-size: var(--size-font-small);
    color: var(--color-stack-subline);
  }
  [data-nextjs-call-stack-frame] > div > svg {
    width: auto;
    height: var(--size-font-small);
    margin-left: var(--size-gap);

    display: none;
  }

  [data-nextjs-call-stack-frame] > div[data-has-source] {
    cursor: pointer;
  }
  [data-nextjs-call-stack-frame] > div[data-has-source]:hover {
    text-decoration: underline dotted;
  }
  [data-nextjs-call-stack-frame] > div[data-has-source] > svg {
    display: unset;
  }
`},403:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.getErrorByType=void 0;const n=r(851);const o=r(233);const a=r(504);async function getErrorByType(e){const{id:t,event:r}=e;switch(r.type){case n.TYPE_UNHANDLED_ERROR:case n.TYPE_UNHANDLED_REJECTION:{const e={id:t,runtime:true,error:r.reason,frames:await(0,a.getOriginalStackFrames)(r.frames,(0,o.getErrorSource)(r.reason),r.reason.toString())};if(r.type===n.TYPE_UNHANDLED_ERROR){e.componentStack=r.componentStack}return e}default:{break}}const i=r;throw new Error("type system invariant violation")}t.getErrorByType=getErrorByType},233:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.getServerError=t.decorateServerError=t.getErrorSource=t.getFilesystemFrame=void 0;const n=r(974);function getFilesystemFrame(e){const t={...e};if(typeof t.file==="string"){if(t.file.startsWith("/")||/^[a-z]:\\/i.test(t.file)||t.file.startsWith("\\\\")){t.file=`file://${t.file}`}}return t}t.getFilesystemFrame=getFilesystemFrame;const o=Symbol("NextjsError");function getErrorSource(e){return e[o]||null}t.getErrorSource=getErrorSource;function decorateServerError(e,t){Object.defineProperty(e,o,{writable:false,enumerable:false,configurable:false,value:t})}t.decorateServerError=decorateServerError;function getServerError(e,t){let r;try{throw new Error(e.message)}catch(e){r=e}r.name=e.name;try{r.stack=`${r.toString()}\n${(0,n.parse)(e.stack).map(getFilesystemFrame).map((e=>{let t=`    at ${e.methodName}`;if(e.file){let r=e.file;if(e.lineNumber){r+=`:${e.lineNumber}`;if(e.column){r+=`:${e.column}`}}t+=` (${r})`}return t})).join("\n")}`}catch{r.stack=e.stack}decorateServerError(r,t);return r}t.getServerError=getServerError},910:function(e,t){Object.defineProperty(t,"__esModule",{value:true});t.noop=void 0;function noop(e,...t){const r=e.length-1;return e.slice(0,r).reduce(((e,r,n)=>e+r+t[n]),"")+e[r]}t.noop=noop},636:function(e,t,r){Object.defineProperty(t,"__esModule",{value:true});t.parseStack=void 0;const n=r(974);const o=/\/_next(\/static\/.+)/;function parseStack(e){const t=(0,n.parse)(e);return t.map((e=>{try{const t=new URL(e.file);const r=o.exec(t.pathname);if(r){const t=process.env.__NEXT_DIST_DIR?.replace(/\\/g,"/")?.replace(/\/$/,"");if(t){e.file="file://"+t.concat(r.pop())}}}catch{}return e}))}t.parseStack=parseStack},504:function(e,t){Object.defineProperty(t,"__esModule",{value:true});t.getFrameSource=t.getOriginalStackFrames=t.getOriginalStackFrame=void 0;function getOriginalStackFrame(e,t,r){async function _getOriginalStackFrame(){const n=new URLSearchParams;n.append("isServer",String(t==="server"));n.append("isEdgeServer",String(t==="edge-server"));n.append("errorMessage",r);for(const t in e){n.append(t,(e[t]??"").toString())}const o=new AbortController;const a=setTimeout((()=>o.abort()),3e3);const i=await self.fetch(`${process.env.__NEXT_ROUTER_BASEPATH||""}/__nextjs_original-stack-frame?${n.toString()}`,{signal:o.signal}).finally((()=>{clearTimeout(a)}));if(!i.ok||i.status===204){return Promise.reject(new Error(await i.text()))}const l=await i.json();return{error:false,reason:null,external:false,expanded:!Boolean((e.file?.includes("node_modules")||l.originalStackFrame?.file?.includes("node_modules"))??true),sourceStackFrame:e,originalStackFrame:l.originalStackFrame,originalCodeFrame:l.originalCodeFrame||null}}if(!(e.file?.startsWith("webpack-internal:")||e.file?.startsWith("file:"))){return Promise.resolve({error:false,reason:null,external:true,expanded:false,sourceStackFrame:e,originalStackFrame:null,originalCodeFrame:null})}return _getOriginalStackFrame().catch((t=>({error:true,reason:t?.message??t?.toString()??"Unknown Error",external:false,expanded:false,sourceStackFrame:e,originalStackFrame:null,originalCodeFrame:null})))}t.getOriginalStackFrame=getOriginalStackFrame;function getOriginalStackFrames(e,t,r){return Promise.all(e.map((e=>getOriginalStackFrame(e,t,r))))}t.getOriginalStackFrames=getOriginalStackFrames;function getFrameSource(e){let t="";try{const r=new URL(e.file);if(typeof globalThis!=="undefined"&&globalThis.location?.origin!==r.origin){if(r.origin==="null"){t+=r.protocol}else{t+=r.origin}}t+=r.pathname;t+=" "}catch{t+=(e.file||"(unknown)")+" "}if(e.lineNumber!=null){if(e.column!=null){t+=`(${e.lineNumber}:${e.column}) `}else{t+=`(${e.lineNumber}) `}}return t.slice(0,-1)}t.getFrameSource=getFrameSource},169:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.useOnClickOutside=void 0;const i=a(r(522));function useOnClickOutside(e,t){i.useEffect((()=>{if(e==null||t==null){return}const listener=r=>{if(!e||e.contains(r.target)){return}t(r)};const r=e.getRootNode();r.addEventListener("mousedown",listener);r.addEventListener("touchstart",listener);return function(){r.removeEventListener("mousedown",listener);r.removeEventListener("touchstart",listener)}}),[t,e])}t.useOnClickOutside=useOnClickOutside},865:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.CloseIcon=void 0;const i=a(r(522));const CloseIcon=()=>i.createElement("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i.createElement("path",{d:"M18 6L6 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),i.createElement("path",{d:"M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}));t.CloseIcon=CloseIcon},884:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.Base=void 0;const i=a(r(522));const l=r(910);function Base(){return i.createElement("style",null,(0,l.noop)`
        :host {
          --size-gap-half: 4px;
          --size-gap: 8px;
          --size-gap-double: 16px;
          --size-gap-triple: 24px;
          --size-gap-quad: 32px;

          --size-font-small: 14px;
          --size-font: 16px;
          --size-font-big: 20px;
          --size-font-bigger: 24px;

          --color-background: white;
          --color-font: #757575;
          --color-backdrop: rgba(17, 17, 17, 0.2);

          --color-stack-h6: #222;
          --color-stack-headline: #666;
          --color-stack-subline: #999;

          --color-accents-1: #808080;
          --color-accents-2: #222222;
          --color-accents-3: #404040;

          --font-stack-monospace: 'SFMono-Regular', Consolas, 'Liberation Mono',
            Menlo, Courier, monospace;

          --color-ansi-selection: rgba(95, 126, 151, 0.48);
          --color-ansi-bg: #111111;
          --color-ansi-fg: #cccccc;

          --color-ansi-white: #777777;
          --color-ansi-black: #141414;
          --color-ansi-blue: #00aaff;
          --color-ansi-cyan: #88ddff;
          --color-ansi-green: #98ec65;
          --color-ansi-magenta: #aa88ff;
          --color-ansi-red: #ff5555;
          --color-ansi-yellow: #ffcc33;
          --color-ansi-bright-white: #ffffff;
          --color-ansi-bright-black: #777777;
          --color-ansi-bright-blue: #33bbff;
          --color-ansi-bright-cyan: #bbecff;
          --color-ansi-bright-green: #b6f292;
          --color-ansi-bright-magenta: #cebbff;
          --color-ansi-bright-red: #ff8888;
          --color-ansi-bright-yellow: #ffd966;
        }

        @media (prefers-color-scheme: dark) {
          :host {
            --color-background: rgb(28, 28, 30);
            --color-font: white;
            --color-backdrop: rgb(44, 44, 46);

            --color-stack-h6: rgb(200, 200, 204);
            --color-stack-headline: rgb(99, 99, 102);
            --color-stack-subline: rgba(142, 142, 147);

            --color-accents-3: rgb(118, 118, 118);
          }
        }

        .mono {
          font-family: var(--font-stack-monospace);
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-bottom: var(--size-gap);
          font-weight: 500;
          line-height: 1.5;
        }
      `)}t.Base=Base},464:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.ComponentStyles=void 0;const i=a(r(522));const l=r(399);const s=r(651);const u=r(543);const c=r(993);const d=r(488);const f=r(120);const m=r(936);const p=r(355);const b=r(484);const v=r(910);function ComponentStyles(){return i.createElement("style",null,(0,v.noop)`
        ${c.styles}
        ${f.styles}
        ${s.styles}
        ${u.styles}
        ${l.styles}
        ${d.styles}
        
        ${m.styles}
        ${p.styles}
        ${b.styles}
      `)}t.ComponentStyles=ComponentStyles},495:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var o=Object.getOwnPropertyDescriptor(t,r);if(!o||("get"in o?!t.__esModule:o.writable||o.configurable)){o={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,o)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);o(t,e);return t};Object.defineProperty(t,"__esModule",{value:true});t.CssReset=void 0;const i=a(r(522));const l=r(910);function CssReset(){return i.createElement("style",null,(0,l.noop)`
        :host {
          all: initial;

          /* the direction property is not reset by 'all' */
          direction: ltr;
        }

        /*!
         * Bootstrap Reboot v4.4.1 (https://getbootstrap.com/)
         * Copyright 2011-2019 The Bootstrap Authors
         * Copyright 2011-2019 Twitter, Inc.
         * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
         * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
         */
        *,
        *::before,
        *::after {
          box-sizing: border-box;
        }

        :host {
          font-family: sans-serif;
          line-height: 1.15;
          -webkit-text-size-adjust: 100%;
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        article,
        aside,
        figcaption,
        figure,
        footer,
        header,
        hgroup,
        main,
        nav,
        section {
          display: block;
        }

        :host {
          margin: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
            'Noto Color Emoji';
          font-size: 16px;
          font-weight: 400;
          line-height: 1.5;
          color: var(--color-font);
          text-align: left;
          background-color: #fff;
        }

        [tabindex='-1']:focus:not(:focus-visible) {
          outline: 0 !important;
        }

        hr {
          box-sizing: content-box;
          height: 0;
          overflow: visible;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 0;
          margin-bottom: 8px;
        }

        p {
          margin-top: 0;
          margin-bottom: 16px;
        }

        abbr[title],
        abbr[data-original-title] {
          text-decoration: underline;
          -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
          cursor: help;
          border-bottom: 0;
          -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
        }

        address {
          margin-bottom: 16px;
          font-style: normal;
          line-height: inherit;
        }

        ol,
        ul,
        dl {
          margin-top: 0;
          margin-bottom: 16px;
        }

        ol ol,
        ul ul,
        ol ul,
        ul ol {
          margin-bottom: 0;
        }

        dt {
          font-weight: 700;
        }

        dd {
          margin-bottom: 8px;
          margin-left: 0;
        }

        blockquote {
          margin: 0 0 16px;
        }

        b,
        strong {
          font-weight: bolder;
        }

        small {
          font-size: 80%;
        }

        sub,
        sup {
          position: relative;
          font-size: 75%;
          line-height: 0;
          vertical-align: baseline;
        }

        sub {
          bottom: -0.25em;
        }

        sup {
          top: -0.5em;
        }

        a {
          color: #007bff;
          text-decoration: none;
          background-color: transparent;
        }

        a:hover {
          color: #0056b3;
          text-decoration: underline;
        }

        a:not([href]) {
          color: inherit;
          text-decoration: none;
        }

        a:not([href]):hover {
          color: inherit;
          text-decoration: none;
        }

        pre,
        code,
        kbd,
        samp {
          font-family: SFMono-Regular, Menlo, Monaco, Consolas,
            'Liberation Mono', 'Courier New', monospace;
          font-size: 1em;
        }

        pre {
          margin-top: 0;
          margin-bottom: 16px;
          overflow: auto;
        }

        figure {
          margin: 0 0 16px;
        }

        img {
          vertical-align: middle;
          border-style: none;
        }

        svg {
          overflow: hidden;
          vertical-align: middle;
        }

        table {
          border-collapse: collapse;
        }

        caption {
          padding-top: 12px;
          padding-bottom: 12px;
          color: #6c757d;
          text-align: left;
          caption-side: bottom;
        }

        th {
          text-align: inherit;
        }

        label {
          display: inline-block;
          margin-bottom: 8px;
        }

        button {
          border-radius: 0;
        }

        button:focus {
          outline: 1px dotted;
          outline: 5px auto -webkit-focus-ring-color;
        }

        input,
        button,
        select,
        optgroup,
        textarea {
          margin: 0;
          font-family: inherit;
          font-size: inherit;
          line-height: inherit;
        }

        button,
        input {
          overflow: visible;
        }

        button,
        select {
          text-transform: none;
        }

        select {
          word-wrap: normal;
        }

        button,
        [type='button'],
        [type='reset'],
        [type='submit'] {
          -webkit-appearance: button;
        }

        button:not(:disabled),
        [type='button']:not(:disabled),
        [type='reset']:not(:disabled),
        [type='submit']:not(:disabled) {
          cursor: pointer;
        }

        button::-moz-focus-inner,
        [type='button']::-moz-focus-inner,
        [type='reset']::-moz-focus-inner,
        [type='submit']::-moz-focus-inner {
          padding: 0;
          border-style: none;
        }

        input[type='radio'],
        input[type='checkbox'] {
          box-sizing: border-box;
          padding: 0;
        }

        input[type='date'],
        input[type='time'],
        input[type='datetime-local'],
        input[type='month'] {
          -webkit-appearance: listbox;
        }

        textarea {
          overflow: auto;
          resize: vertical;
        }

        fieldset {
          min-width: 0;
          padding: 0;
          margin: 0;
          border: 0;
        }

        legend {
          display: block;
          width: 100%;
          max-width: 100%;
          padding: 0;
          margin-bottom: 8px;
          font-size: 24px;
          line-height: inherit;
          color: inherit;
          white-space: normal;
        }

        progress {
          vertical-align: baseline;
        }

        [type='number']::-webkit-inner-spin-button,
        [type='number']::-webkit-outer-spin-button {
          height: auto;
        }

        [type='search'] {
          outline-offset: -2px;
          -webkit-appearance: none;
        }

        [type='search']::-webkit-search-decoration {
          -webkit-appearance: none;
        }

        ::-webkit-file-upload-button {
          font: inherit;
          -webkit-appearance: button;
        }

        output {
          display: inline-block;
        }

        summary {
          display: list-item;
          cursor: pointer;
        }

        template {
          display: none;
        }

        [hidden] {
          display: none !important;
        }
      `)}t.CssReset=CssReset},997:function(e){e.exports=require("next/dist/compiled/anser")},292:function(e){e.exports=require("next/dist/compiled/css.escape")},709:function(e){e.exports=require("next/dist/compiled/platform")},974:function(e){e.exports=require("next/dist/compiled/stacktrace-parser")},518:function(e){e.exports=require("next/dist/compiled/strip-ansi")},522:function(e){e.exports=require("react")},255:function(e){e.exports=require("react-dom")}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var o=t[r]={exports:{}};var a=true;try{e[r].call(o.exports,o,o.exports,__nccwpck_require__);a=false}finally{if(a)delete t[r]}return o.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(204);module.exports=r})();